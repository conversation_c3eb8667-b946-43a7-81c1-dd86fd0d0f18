import { JsonObject } from 'type-fest'
import { Injectable, Logger } from '@nestjs/common'
import { get, set, cloneDeep } from 'lodash'
import { Op, Transaction } from '@infra-node/sequelize'

import { DatabaseService } from '../../../database/database.service'
import {
  MaterialSmartDescription,
  ModelManager,
} from '../../../database/models'
import { MaterialSmartDescriptionQueryService } from './query.service'
import { PUBLISH_STATUS } from '../constants'

/**
 * 物料智能描述创建服务
 * 专门负责创建和更新操作，遵循单一职责原则
 */
@Injectable()
export class MaterialSmartDescriptionCreationService {
  private readonly logger = new Logger(
    MaterialSmartDescriptionCreationService.name,
  )

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly queryService: MaterialSmartDescriptionQueryService,
  ) {}

  /**
   * 创建智能描述记录
   */
  async createSmartDescription(params: {
    jobId: number
    materialId: number
    materialPubId: number
    materialVersion: string
    result: Service.MaterialSmartDescription.BasicMaterialDescription
    namespace: string
    schemaUrl: string
  }): Promise<MaterialSmartDescription> {
    const now = Date.now()
    const uuid = ModelManager.genPrimaryIndex()
    const models = this.databaseService.getModels()

    // 判断该物料是否已有任何版本的智能描述
    const publishStatus = await this.determinePublishStatus(params.materialId, params.materialVersion)

    const description = await models.MaterialSmartDescription.create({
      id: uuid,
      materialId: params.materialId,
      materialPubId: params.materialPubId,
      materialVersion: params.materialVersion,
      jobId: params.jobId,
      smartDescription: params.result as unknown as JsonObject,
      createTime: now,
      state: 1,
      namespace: params.namespace,
      schemaUrl: params.schemaUrl,
      publishStatus, // 根据业务逻辑设置发布状态
    })

    this.logger.log(
      `创建智能描述成功 materialId: ${params.materialId}, version: ${params.materialVersion}, id: ${description.id}, publishStatus: ${publishStatus}`,
    )
    return description.toJSON() as unknown as MaterialSmartDescription
  }

  /**
   * 基于现有记录创建新的智能描述记录
   * 保留原有记录的基本信息，但使用新的 smartDescription 和 createTime
   */
  async createSmartDescriptionFromExisting(params: {
    originalDescription: MaterialSmartDescription
    updatedSmartDescription: Service.MaterialSmartDescription.BasicMaterialDescription
  }): Promise<MaterialSmartDescription | null> {
    try {
      const { originalDescription, updatedSmartDescription } = params
      const now = Date.now()
      const uuid = ModelManager.genPrimaryIndex()
      const models = this.databaseService.getModels()

      const newDescription = await models.MaterialSmartDescription.create({
        id: uuid,
        materialId: originalDescription.materialId,
        materialPubId: originalDescription.materialPubId,
        materialVersion: originalDescription.materialVersion,
        jobId: originalDescription.jobId, // 保持原有的 jobId
        smartDescription: updatedSmartDescription as unknown as JsonObject,
        createTime: now, // 使用当前时间作为新的创建时间
        state: 1, // 新记录状态为正常
        namespace: originalDescription.namespace, // 保持原有的 namespace
        schemaUrl: originalDescription.schemaUrl, // 从 Job 表继承最新的 schemaUrl
        publishStatus: originalDescription.publishStatus, // 保持原有的发布状态
      })

      this.logger.log(
        `基于记录 ${originalDescription.id} 创建新的智能描述记录: ${newDescription.id}`,
      )
      return newDescription.toJSON() as unknown as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error('基于现有记录创建新描述失败', error)
      return null
    }
  }

  /**
   * 在事务中基于现有记录创建新的智能描述记录
   * 保留原有记录的基本信息，但使用新的 smartDescription 和 createTime
   */
  private async createSmartDescriptionFromExistingInTransaction(params: {
    originalDescription: MaterialSmartDescription
    updatedSmartDescription: Service.MaterialSmartDescription.BasicMaterialDescription
    transaction: Transaction
  }): Promise<MaterialSmartDescription | null> {
    try {
      const { originalDescription, updatedSmartDescription, transaction } = params
      const now = Date.now()
      const uuid = ModelManager.genPrimaryIndex()
      const models = this.databaseService.getModels()

      const newDescription = await models.MaterialSmartDescription.create({
        id: uuid,
        materialId: originalDescription.materialId,
        materialPubId: originalDescription.materialPubId,
        materialVersion: originalDescription.materialVersion,
        jobId: originalDescription.jobId, // 保持原有的 jobId
        smartDescription: updatedSmartDescription as unknown as JsonObject,
        createTime: now, // 使用当前时间作为新的创建时间
        state: 1, // 新记录状态为正常
        namespace: originalDescription.namespace, // 保持原有的 namespace
        schemaUrl: originalDescription.schemaUrl, // 从 Job 表继承最新的 schemaUrl
        publishStatus: originalDescription.publishStatus, // 保持原有的发布状态
      }, { transaction })

      this.logger.log(
        `在事务中基于记录 ${originalDescription.id} 创建新的智能描述记录: ${newDescription.id}`,
      )
      return newDescription.toJSON() as unknown as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error('在事务中基于现有记录创建新描述失败', error)
      return null
    }
  }

  /**
   * 更新 ZhiDa 描述
   * 创建新记录而不是更新现有记录，并禁用其他同物料同版本的描述
   */
  async updateZhidaDescription(
    params: Service.MaterialSmartDescription.UpdateZhidaDescriptionParams,
  ): Promise<Service.MaterialSmartDescription.UpdateZhidaDescriptionResult> {
    const sequelize = this.databaseService.getSequelize()
    const transaction = await sequelize.transaction()

    try {
      // 使用查询服务查找原始记录
      const originalDescription = await this.queryService.findDescriptionById(params.id)

      if (!originalDescription) {
        await transaction.rollback()
        this.logger.error(`未找到指定的描述记录 ID: ${params.id}`)
        return {
          success: false,
          error: `未找到指定的描述记录 ID: ${params.id}`,
        }
      }

      // 检查原始记录的 smartDescription 是否有效
      if (!originalDescription.smartDescription) {
        await transaction.rollback()
        this.logger.error(`记录 ID: ${params.id} 的 smartDescription 字段为空`)
        return {
          success: false,
          error: `记录 ID: ${params.id} 的 smartDescription 字段为空，无法更新`,
        }
      }

      // 深拷贝原始的 smartDescription
      const updatedSmartDescription = cloneDeep(
        originalDescription.smartDescription as unknown as Service.MaterialSmartDescription.BasicMaterialDescription,
      )

      // 验证深拷贝后的数据
      if (!updatedSmartDescription) {
        await transaction.rollback()
        this.logger.error(`记录 ID: ${params.id} 的 smartDescription 深拷贝失败`)
        return {
          success: false,
          error: `记录 ID: ${params.id} 的 smartDescription 数据无效`,
        }
      }

      // 获取当前的 payload
      const payload = get(updatedSmartDescription, 'payload', {}) as Record<
        string,
        unknown
      >

      // 根据传入的 updateDto 更新对应字段
      const { updateDto } = params

      if (updateDto.description !== undefined) {
        payload.description = updateDto.description
      }

      if (updateDto.propsDefine !== undefined) {
        payload.propsDefine = updateDto.propsDefine
      }

      if (updateDto.jsxDemo !== undefined) {
        payload.jsxDemo = updateDto.jsxDemo
      }

      if (updateDto.childNested !== undefined) {
        payload.childNested = updateDto.childNested
      }

      if (updateDto.jsxPropCompatible !== undefined) {
        payload.jsxPropCompatible = updateDto.jsxPropCompatible
      }

      if (updateDto.mergePropsBeforeInsert !== undefined) {
        payload.mergePropsBeforeInsert = updateDto.mergePropsBeforeInsert
      }

      if (updateDto.purePropEffect !== undefined) {
        payload.purePropEffect = updateDto.purePropEffect
      }

      // 将更新后的 payload 设置回 smartDescription
      set(updatedSmartDescription, 'payload', payload)

      // 在事务中创建新记录
      const newDescription = await this.createSmartDescriptionFromExistingInTransaction({
        originalDescription,
        updatedSmartDescription,
        transaction,
      })

      if (!newDescription) {
        await transaction.rollback()
        return {
          success: false,
          error: '创建新记录失败',
        }
      }

      // 禁用其他同物料同版本的描述记录
      const disabledCount = await this.disableOtherVersionDescriptions(
        originalDescription.materialId,
        originalDescription.materialVersion,
        newDescription.id,
        transaction,
      )

      // 提交事务
      await transaction.commit()

      this.logger.log(
        `更新 ZhiDa 描述成功 - 新记录ID: ${newDescription.id}, 禁用旧记录数量: ${disabledCount}`,
      )

      return {
        success: true,
        data: newDescription as unknown as Service.MaterialSmartDescription.EnrichedDescription,
      }
    }
    catch (error) {
      await transaction.rollback()
      this.logger.error(`更新 ZhiDa 描述失败 ID: ${params.id}`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 批量创建智能描述记录
   */
  async batchCreateSmartDescriptions(
    descriptions: Array<{
      jobId: number
      materialId: number
      materialPubId: number
      materialVersion: string
      result: Service.MaterialSmartDescription.BasicMaterialDescription
      namespace: string
      schemaUrl: string
    }>,
  ): Promise<MaterialSmartDescription[]> {
    const results: MaterialSmartDescription[] = []

    for (const params of descriptions) {
      try {
        const description = await this.createSmartDescription(params)
        results.push(description)
      }
      catch (error) {
        this.logger.error(
          `批量创建中单个记录失败 materialId: ${params.materialId}`,
          error,
        )
        // 继续处理其他记录，不中断整个批量操作
      }
    }

    this.logger.log(
      `批量创建完成，成功: ${results.length}/${descriptions.length}`,
    )
    return results
  }

  /**
   * 软删除描述记录（设置 state = -1）
   */
  async softDeleteDescription(id: number): Promise<boolean> {
    try {
      const models = this.databaseService.getModels()
      const [affectedCount] = await models.MaterialSmartDescription.update(
        { state: -1 },
        {
          where: {
            id,
            state: 1, // 只能删除正常状态的记录
          },
        },
      )

      const success = affectedCount > 0
      if (success) {
        this.logger.log(`软删除描述记录成功 id: ${id}`)
      }
      else {
        this.logger.warn(`软删除描述记录失败，记录不存在或已删除 id: ${id}`)
      }

      return success
    }
    catch (error) {
      this.logger.error(`软删除描述记录失败 id: ${id}`, error)
      return false
    }
  }

  /**
   * 更新发布状态
   */
  async updatePublishStatus(
    id: number,
    publishStatus: number,
  ): Promise<{ success: boolean, message: string }> {
    try {
      const models = this.databaseService.getModels()
      const [affectedCount] = await models.MaterialSmartDescription.update(
        { publishStatus },
        {
          where: {
            id,
            state: 1, // 只能更新正常状态的记录
          },
        },
      )

      const success = affectedCount > 0
      if (success) {
        this.logger.log(
          `更新发布状态成功 id: ${id}, publishStatus: ${publishStatus}`,
        )
        return {
          success: true,
          message: '更新发布状态成功',
        }
      }
      else {
        this.logger.warn(`更新发布状态失败，记录不存在 id: ${id}`)
        return {
          success: false,
          message: '记录不存在或已删除',
        }
      }
    }
    catch (error) {
      this.logger.error(`更新发布状态失败 id: ${id}`, error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 禁用其他同物料同版本的描述记录
   * 将同 materialId + materialVersion 的其他记录的 state 设置为 -1
   */
  private async disableOtherVersionDescriptions(
    materialId: number,
    materialVersion: string,
    excludeId: number,
    transaction: Transaction,
  ): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const [affectedCount] = await models.MaterialSmartDescription.update(
        { state: -1 }, // 设置为禁用状态
        {
          where: {
            materialId,
            materialVersion,
            state: 1, // 只禁用正常状态的记录
            id: {
              [Op.ne]: excludeId, // 排除新创建的记录
            },
          },
          transaction,
        },
      )

      this.logger.log(
        `禁用同物料同版本的其他描述记录 - materialId: ${materialId}, version: ${materialVersion}, 禁用数量: ${affectedCount}`,
      )

      return affectedCount
    }
    catch (error) {
      this.logger.error(
        `禁用其他版本描述记录失败 - materialId: ${materialId}, version: ${materialVersion}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据业务规则确定发布状态
   * 规则：与 query-with-fallback 逻辑一致，当前物料的当前版本"往前"找，没有可用的描述时才为正式发布状态
   * 否则默认为草稿状态
   */
  private async determinePublishStatus(
    materialId: number,
    materialVersion: string,
  ): Promise<number> {
    try {
      // 使用与 query-with-fallback 相同的逻辑判断是否需要回退
      // 如果需要回退，说明当前版本及往前版本都没有可用描述，则设为正式发布
      // 否则设为草稿状态
      const shouldUseFallback = await this.shouldUseFallbackForVersion(
        materialId,
        materialVersion,
      )

      if (shouldUseFallback) {
        this.logger.log(
          `物料 ${materialId} 版本 ${materialVersion} 及往前版本没有可用描述，新描述将设置为正式发布状态`,
        )
        return PUBLISH_STATUS.PUBLISHED
      }
      else {
        this.logger.log(
          `物料 ${materialId} 版本 ${materialVersion} 往前版本有可用描述，新描述将设置为草稿状态`,
        )
        return PUBLISH_STATUS.DRAFT
      }
    }
    catch (error) {
      this.logger.error(
        `确定发布状态失败 - materialId: ${materialId}, version: ${materialVersion}`,
        error,
      )
      // 出错时默认为草稿状态，更安全
      return PUBLISH_STATUS.DRAFT
    }
  }

  /**
   * 将草稿状态的智能描述转为正式发布状态
   * 同时将同一物料同一版本的其他描述设置为禁用状态
   * 支持在审核时同时修改内容
   */
  async publishDraftDescription(
    descriptionId: number,
    updateDto?: Service.MaterialSmartDescription.UpdateZhidaDescriptionDto,
  ): Promise<MaterialSmartDescription> {
    const models = this.databaseService.getModels()
    const transaction = await this.databaseService.getSequelize().transaction()

    try {
      // 查找目标描述记录（使用与查询接口相同的逻辑，只查询有效状态的记录）
      const targetDescription = await models.MaterialSmartDescription.findOne({
        where: {
          id: descriptionId,
          state: 1, // 只查询正常状态的记录
        },
        transaction,
      })

      if (!targetDescription) {
        throw new Error(`智能描述记录不存在或状态无效: ${descriptionId}`)
      }

      // 添加调试日志
      this.logger.log(
        `发布草稿描述 - 记录详情: id=${descriptionId}, state=${targetDescription.state}, publishStatus=${targetDescription.publishStatus}, materialId=${targetDescription.materialId}, version=${targetDescription.materialVersion}`,
      )

      if (targetDescription.publishStatus === PUBLISH_STATUS.PUBLISHED) {
        throw new Error(`智能描述已是正式发布状态: ${descriptionId}`)
      }

      // 禁用同一物料同一版本的其他已发布描述
      await this.disableOtherVersionDescriptions(
        targetDescription.materialId,
        targetDescription.materialVersion,
        descriptionId,
        transaction,
      )

      // 如果提供了更新内容，先更新内容
      let updatedSmartDescription = targetDescription.smartDescription
      if (updateDto) {
        updatedSmartDescription = this.updateSmartDescriptionContent(
          targetDescription.smartDescription,
          updateDto,
        ) as JsonObject
      }

      // 更新目标描述为正式发布状态，同时更新内容（如果有）
      await targetDescription.update(
        {
          publishStatus: PUBLISH_STATUS.PUBLISHED,
          smartDescription: updatedSmartDescription,
        },
        { transaction },
      )

      await transaction.commit()

      this.logger.log(
        `成功将草稿描述转为正式发布状态 - id: ${descriptionId}, materialId: ${targetDescription.materialId}, version: ${targetDescription.materialVersion}`,
      )

      return targetDescription.toJSON() as unknown as MaterialSmartDescription
    }
    catch (error) {
      await transaction.rollback()
      this.logger.error(
        `将草稿转为正式发布状态失败 - id: ${descriptionId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 判断指定物料版本是否需要使用回退逻辑
   * 修正逻辑：检查是否有任何有效描述（包括草稿状态），而不仅仅是已发布的描述
   */
  private async shouldUseFallbackForVersion(
    materialId: number,
    materialVersion: string,
  ): Promise<boolean> {
    try {
      // 构造物料标识参数
      const params: Service.Forward.MaterialPlatform.MaterialIdentifierParams = {
        materialId,
        materialVersionName: materialVersion,
      }

      // 检查目标版本是否有任何有效描述（包括草稿状态）
      const targetDescription = await this.queryService.findLatestDescriptionByMaterialIdentifier(params)

      if (targetDescription) {
        // 如果目标版本有有效描述，不需要回退
        return false
      }

      // 目标版本没有有效描述，检查是否有其他版本的有效描述
      const allDescriptions = await this.queryService.findDescriptionsByMaterialId(materialId)

      // 如果该物料完全没有任何描述，需要回退（设为正式发布）
      return allDescriptions.length === 0
    }
    catch (error) {
      this.logger.error(
        `判断是否需要回退失败 - materialId: ${materialId}, version: ${materialVersion}`,
        error,
      )
      // 出错时返回 true，表示需要回退（更安全，设为正式发布）
      return true
    }
  }

  /**
   * 更新 smartDescription 内容
   * 基于现有的更新逻辑，但不创建新记录，只返回更新后的内容
   */
  private updateSmartDescriptionContent(
    originalSmartDescription: unknown,
    updateDto: Service.MaterialSmartDescription.UpdateZhidaDescriptionDto,
  ): unknown {
    try {
      // 深拷贝原始的 smartDescription
      const updatedSmartDescription = cloneDeep(
        originalSmartDescription as unknown as Service.MaterialSmartDescription.BasicMaterialDescription,
      )

      if (!updatedSmartDescription) {
        throw new Error('smartDescription 数据无效')
      }

      // 获取当前的 payload
      const payload = get(updatedSmartDescription, 'payload', {}) as Record<
        string,
        unknown
      >

      // 根据传入的 updateDto 更新对应字段
      if (updateDto.description !== undefined) {
        payload.description = updateDto.description
      }

      if (updateDto.propsDefine !== undefined) {
        payload.propsDefine = updateDto.propsDefine
      }

      if (updateDto.jsxDemo !== undefined) {
        payload.jsxDemo = updateDto.jsxDemo
      }

      if (updateDto.childNested !== undefined) {
        payload.childNested = updateDto.childNested
      }

      if (updateDto.jsxPropCompatible !== undefined) {
        payload.jsxPropCompatible = updateDto.jsxPropCompatible
      }

      if (updateDto.mergePropsBeforeInsert !== undefined) {
        payload.mergePropsBeforeInsert = updateDto.mergePropsBeforeInsert
      }

      if (updateDto.purePropEffect !== undefined) {
        payload.purePropEffect = updateDto.purePropEffect
      }

      // 将更新后的 payload 设置回 smartDescription
      set(updatedSmartDescription, 'payload', payload)

      return updatedSmartDescription
    }
    catch (error) {
      this.logger.error('更新 smartDescription 内容失败', error)
      // 如果更新失败，返回原始内容
      return originalSmartDescription
    }
  }
}
